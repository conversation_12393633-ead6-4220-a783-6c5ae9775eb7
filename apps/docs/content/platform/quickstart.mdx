---
title: "Quick Start"

description: "Quickly transform your ideas into production-ready web applications using the Libra AI platform"

mode: "center"

icon: Rocket

---

Welcome to Libra! This guide will help you create and customize your first AI-powered website. Let's get started!

## From Concept to Live Web Application

This guide will walk you through turning your ideas into production-ready web applications using Libra's AI-driven platform. Follow these simple steps to bring your vision to life without the hassle of traditional development.

## 1. Define Your Idea

Start by clearly articulating your web application concept. A well-crafted prompt will lay the foundation for a tailored codebase.

**Example Prompt:**

```
"Develop a modern landing page for a tech startup, featuring interactive UI elements and bold, vibrant colors."
```

> **Pro Tip:** The more specific your description, the better the resulting application will match your expectations. Include details such as design style, functional requirements, and target audience.

![Libra Platform Demo](https://media.libra.dev/Area.gif)

## 2. Refine Your Idea

Use the "Enhance Prompt" feature to let Libra's advanced AI refine your initial concept. This step helps ensure the generated web application aligns closely with your vision.

- **Review AI Suggestions:** Examine the enhancements and fine-tune as needed

- **Confirm Details:** Adjust and finalize your prompt for optimal results

## 3. Generate Your Application

Once your prompt is refined, click the "Submit" button to initiate the generation process. Libra will transform your enhanced prompt into a fully customized, production-ready codebase built on a React stack.

> **Generation Process:** Typically takes a few minutes, with real-time progress updates displayed. You can view the generation logs for detailed insights during this time.

## 4. Edit and Customize

Enter the Libra workspace to fine-tune your project. Further enhance your application by:

- **Iterative Editing:** Continuously improve using AI-driven suggestions

- **Component Integration:** Seamlessly add and customize UI components such as popups, buttons, and input fields

- **Optional AI Enhancements:** Integrate OpenAI capabilities to elevate your application's functionality

## 5. Export Your Project

When you're ready, choose an export method to save your work:

- **File Download:** Export your project as a ZIP file

- **GitHub Export:** Automatically create a new repository on GitHub containing your project code

## 6. Deploy Your Web Application

Deploy your application with a single click. Enjoy seamless hosting integrations and continuous delivery, ensuring your web application is secure, scalable, and live from day one.

> **Deployment Options:** Currently supports deployment on Cloudflare Workers. Support for additional platforms like Vercel and Netlify is planned for future releases.

⚠️: For now, deployment is only supported on Cloudflare

## Next Steps

By following these steps, you'll be able to transform your ideas into production-ready web applications using Libra. Let our powerful AI tools handle the technical details while you focus on bringing your vision to life.

**Recommended Next Actions:**

- Explore the [User Guide](/platform/usinglibra) to learn about advanced features

- Visit the [FAQ](/platform/faq) for answers to common questions

- Join our [GitHub Community](https://github.com/nextify-limited/libra/discussions) to connect with other developers

Happy building!