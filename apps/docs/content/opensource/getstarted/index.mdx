---
title: "Getting Started"
mode: "center"
icon: Album
---

import {Silver} from "../../../components/silver";
import {Gold} from "../../../components/gold";
import {Bronze} from "../../../components/bronze";
import {Platinum} from "../../../components/platinum";

<div className="flex flex-col items-stretch text-center">
    <div style={{height:"170px"}}>

<img  className="block dark:hidden mx-auto my-0! h-full" alt="Libra logo" src="/logo.svg"  />
<img  className="hidden dark:block mx-auto my-0! h-full" alt="Libra logo" src="/logo-dark.svg"  />
    </div>
    <h1 className="text-3xl font-bold">Libra</h1>
    <p className="mt-2 mb-1"></p>
    <div  className="flex flex-row flex-wrap justify-center gap-1 py-2">
        <a className="border-none" href="https://github.com/nextify-limited/nextify-limited/actions/workflows/check.yml">
            <img className="h-[20px] m-0!" src="https://img.shields.io/github/actions/workflow/status/nextify-limited/nextify-limited/ci.yml?label=ci" alt="GitHub Actions Workflow Status" />
        </a>
        <a className="border-none" href="https://github.com/nextify-limited/nextify-limited/blob/main/LICENSE">
            <img className="h-[20px] m-0!" src="https://img.shields.io/badge/License-AGPL-green.svg" alt="GitHub License" />
        </a>
        <a className="border-none" href="https://forum.libra.dev">
            <img className="h-[20px] m-0!" src="https://img.shields.io/badge/Community-Forum-7b8dcd" alt="Community Forum" />
        </a>
        <a className="border-none" href="https://x.com/nextify2024">
            <img className="h-[20px] m-0!" src="https://img.shields.io/badge/made_by-nextify2024-blue?color=FF782B&link=https://x.com/nextify2024" alt="Made by Nextify2024" />
        </a>
        <a className="border-none" href="https://github.com/nextify-limited/libra" rel="nofollow">
            <img  className="h-[20px] m-0!" src="https://img.shields.io/github/stars/nextify-limited/nextify-limited" alt="stars" />
        </a>
    </div>
    <div className="flex flex-row justify-center gap-1">
        <a href="https://libra.dev">Website</a>
        <span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
        <a href="https://forum.libra.dev">Forum</a>
        <span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
        <a href="https://twitter.com/nextify2024">𝕏</a>
        <span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
        <br />
    </div>
</div>

## Introduction

Libra AI is an intelligent development platform that enables users of all skill levels to effortlessly build full-stack web applications.
Simply describe your vision in natural language, and Libra instantly transforms your ideas into fully functional web apps and websites.
No coding experience required — embark on a revolutionary development journey that takes you from concept to deployment faster than ever.

## Features

- **AI-Powered Development**: Transform natural language descriptions into fully functional applications
- **Zero Coding Required**: Build complex web applications without writing a single line of code
- **Full-Stack Capabilities**: Create both frontend interfaces and backend functionality
- **Real-time Preview**: See your application come to life as you describe it
- **Modern Tech Stack**: Built with the latest web technologies and best practices
- **Instant Deployment**: Deploy your applications with a single click
- **Collaborative Environment**: Work with team members in same workspace
- **Extensive Integrations**: Connect with popular tools and services

## What You Can Build

You can build websites and JavaScript-based full stack web applications with Libra. For inspiration, check out our showcase featuring successful applications launched through Libra.

You can create a wide range of applications including:

- **Content Websites**: Blogs, portfolios, marketing sites, and documentation
- **E-commerce Platforms**: Online stores with payment processing and inventory management
- **Productivity Apps**: Task managers, note-taking apps, and collaboration tools
- **Social Applications**: Community platforms, forums, and social networks
- **Business Tools**: CRM systems, dashboards, and analytics platforms
- **Educational Platforms**: Learning management systems and course websites

### Supported Technologies

Libra integrates with a comprehensive ecosystem of modern web development tools:

**Design & Prototyping**
- Figma for design collaboration and asset import (coming soon)

**Deployment & Hosting**
- Cloudflare for edge deployment and performance optimization

**Backend Services**
- coming soon

**Development & Collaboration**
- GitHub for version control, backups, and team collaboration (coming soon)

**Payment Processing**
- Stripe for secure payment handling and subscription management (coming soon)

Learn more about our complete list of supported technologies and recommended architecture patterns in our [Supported Technologies](/docs/technologies) guide.

## For Both Technical Users and Learners

Libra is designed to help users build anything from simple content-focused websites (such as blogs) to complex full-stack web applications (such as productivity apps). It requires clear communication from the user and benefits from some understanding of web development concepts.

### Getting Started Without Coding Experience

You don't need to know how to code when you start out. Building with Libra is an excellent way to learn web development in a practical, hands-on manner, with AI support always available to guide you through the process.

### What You Need to Succeed

To get the most out of Libra, be very clear about:

- **What you want to build**: Describe your application's purpose and functionality
- **User experience goals**: How should users interact with your application
- **Success criteria**: What does a successful implementation look like
- **Design preferences**: Visual style, layout, and branding requirements

To help you get started, read our guides in [Best Practices](/docs/best-practices) to learn how to prompt effectively and work with AI in different development modes.

## Libra and the Ecosystem

Libra is built on a foundation of modern web technologies and integrates seamlessly with the broader development ecosystem. Our platform leverages:

- **Advanced AI Models**: State-of-the-art language models for code generation
- **Cloud Infrastructure**: Scalable and reliable hosting solutions
- **Modern Frameworks**: React, Next.js, and other popular frameworks
- **Development Tools**: Integrated development environment with  collaboration

## Pricing

Libra offers flexible pricing plans to suit different needs and usage patterns. Our pricing is designed to be transparent and scalable:

- **Free Tier**: Perfect for learning and small projects
- **Pro Plans**: Advanced features for professional developers
- **MAX Plans**: Unlimited access to all features
- **Enterprise**: Custom solutions for large organizations

Pricing is based on usage and features accessed. Learn more about [token efficiency](/docs/tokens) and [pricing details](/pricing) on our website.

<br/>

## Community and Support

Join our growing community of developers and creators:

- **Forum**: Get help, share projects, and connect with other users
- **GitHub**: Contribute to open-source components and report issues
- **Documentation**: Comprehensive guides and tutorials
{/* - **Video Tutorials**: Step-by-step walkthroughs and use cases */}

## Sponsors

We're grateful for the support of our sponsors who help make Libra AI possible. Sponsorship at any level is appreciated and helps us continue developing and improving the platform.

If you've built a successful product using Libra or want to support open-source AI development, consider joining our [corporate sponsorship program](https://github.com/sponsors/nextify-limited).

### Sponsorship Tiers

Our sponsorship program offers multiple tiers designed to provide value for sponsors while supporting the Libra ecosystem:

#### Platinum Tier - $2,000+/month
Premium placement with dedicated showcase space, detailed company profile, and maximum visibility across all Libra platforms.

<Platinum locale="en" />

<br/>

#### Gold Tier - $500+/month
Featured placement with company logo, description, and prominent positioning in our sponsor showcase.

<Gold locale="en" />

<br/>

#### Silver Tier - $100+/month
Logo placement in our sponsor grid with direct links to your website or product.

<Silver locale="en" />

<br/>

#### Bronze Tier - $25+/month
Recognition in our community sponsor section with logo display and website link.

<Bronze locale="en" />

<br/>

### Benefits for All Sponsors

- **Community Recognition**: Acknowledgment in our documentation and website
- **GitHub Sponsor Badge**: Official sponsor status on your GitHub profile
- **Forum Access**: Exclusive access to sponsor channels in our Community Forum
- **Early Access**: Preview upcoming features and provide feedback
- **Support Priority**: Faster response times for technical support requests

Ready to become a sponsor? Visit our [GitHub Sponsors page](https://github.com/sponsors/nextify-limited) to get started.