---
title: "常见问题"
mode: "center"
icon: CircleHelp
---

# 常见问题

欢迎来到 Libra AI 常见问题页面！这个综合指南解答了关于使用 Libra 的最常见问题，从入门到高级功能。如果您在这里找不到所需的答案，请不要犹豫，通过 [社区论坛](https://forum.libra.dev) 联系我们的社区，我们的团队和社区成员随时准备提供帮助。

## 入门指南

### 什么是 Libra AI？

Libra AI 是一个开源的全栈应用程序生成平台，结合了 V0 的快速 UI 生成体验和现代开发工具的后端灵活性。它完全可自托管，无供应商锁定，让您完全控制代码、数据和设计系统。

### Libra 与 V0 或 Lovable 有什么不同？

虽然 V0 提供即时 UI 生成但缺乏后端灵活性和自托管选项，其他平台可能将您绑定到特定数据库或计费模式，Libra 结合了两者的优点：

- **完全所有权**：您的域名、数据库、设计系统 - 自由切换
- **闪电般快速开发**：一键 UI + 后端生成，即用即可
- **无限扩展性**：开源、可自托管、无供应商依赖
- **技术栈自由**：支持多种主流框架和数据库组合

### 我需要编程经验才能使用 Libra 吗？

不需要！Libra 设计为适合各种技能水平的用户：

- **初学者**：使用 AI 生成器和模板快速入门
- **中级开发者**：自定义组件和业务逻辑
- **高级开发者**：深度定制和平台扩展

使用 Libra 构建是学习 Web 开发的绝佳方式，以实用、动手的方式进行，AI 支持始终可用来指导您完成整个过程。

### 我可以用 Libra 构建什么？

您可以创建各种应用程序，包括：

- **内容网站**：博客、作品集、营销网站和文档
- **电商平台**：具有支付处理和库存管理的在线商店
- **生产力应用**：任务管理器、笔记应用和协作工具
- **社交应用**：社区平台、论坛和社交网络
- **商业工具**：CRM 系统、仪表板和分析平台
- **教育平台**：学习管理系统和课程网站

## 项目创建和管理

### 如何创建我的第一个项目？

1. **安装依赖**：在项目目录中运行 `bun install`
2. **配置环境**：将 `.env.example` 复制为 `.env` 并填写您的设置
3. **初始化数据库**：按照 `packages/auth/DEV.md` 和 `packages/db/DEV.md` 中的指南操作
4. **开始开发**：运行 `cd apps/web && bun dev`

### 我可以导入现有项目或设计吗？

目前，Libra 专注于从自然语言描述生成新应用程序。与 Figma 等设计工具和 GitHub 等版本控制系统的集成计划在未来版本中推出。

### 如何管理多个项目？

Libra 提供一个集中式仪表板，您可以：
- 在一个地方查看所有项目
- 访问项目设置和配置
- 管理团队访问和权限

### 我可以与团队成员协作吗？

可以

## 技术问题

### Libra 使用什么技术？

Libra 建立在现代技术栈上：

**前端：**
- Next.js 15+
- React 19
- TypeScript
- Tailwind CSS v4.0
- shadcn/ui 组件

**后端：**
- tRPC 用于类型安全 API
- Drizzle ORM 用于数据库操作
- Cloudflare D1 用于认证数据
- NEON PostgreSQL 用于业务数据

**开发：**
- Bun 用于包管理
- Turborepo 用于 monorepo 管理
- GitHub Actions 用于 CI/CD

### 支持哪些数据库？

Libra 支持多种数据库选项：
- **PostgreSQL**（推荐，通过 NEON）
- **Cloudflare D1**（用于认证和权限）

您可以根据需求切换数据库，无供应商锁定。

### 我可以使用自己的设计系统吗？

当然可以！Libra 提供灵活的设计系统选择：
- 使用内置的 Tailwind v4 + shadcn/ui 组件库
- 集成您现有的设计系统
- 完全自定义设计令牌和组件

### 如何部署我的 Libra 应用程序？

目前支持的部署平台：
- ✅ **Cloudflare Workers**（推荐）
- 🔄 **Vercel**（低优先级）
- 🔄 **自托管服务器**（不支持）

详细的部署指南可在我们的[技术文档](https://docs.libra.dev//deploy)中找到。

## 故障排除

### 我的应用程序显示空白页

这通常表示构建或配置问题。尝试以下步骤：

1. **检查控制台**：打开浏览器开发者工具并查找 JavaScript 错误
2. **验证环境变量**：确保 `.env` 中的所有必需变量都正确设置
3. **清除缓存**：删除 `node_modules`、`.turbo` 和 `.next` 目录，然后重新安装依赖
4. **检查数据库连接**：验证您的数据库 URL 和凭据是否正确

### 我遇到控制台错误

常见控制台错误和解决方案：

- **模块未找到**：运行 `bun install` 确保所有依赖都已安装
- **环境变量未定义**：检查您的 `.env` 文件配置
- **数据库连接失败**：验证您的数据库 URL 和网络连接
- **类型错误**：运行 `bun run typecheck` 识别并修复 TypeScript 问题

### 应用程序在移动设备上无响应

Libra 应用程序默认采用响应式设计构建。如果您遇到移动设备问题：

1. **检查视口元标签**：确保在布局中正确配置
2. **测试响应式断点**：使用浏览器开发工具测试不同屏幕尺寸
3. **检查自定义 CSS**：检查是否有自定义样式覆盖响应式行为
4. **更新组件**：确保您使用的是最新版本的 UI 组件

### 构建或部署失败

如果遇到构建问题：

1. **检查依赖**：运行 `bun install` 确保所有包都是最新的
2. **清除构建缓存**：删除 `.turbo` 和构建目录
3. **验证环境**：确保为生产环境设置了所有必需的环境变量
4. **检查日志**：查看构建日志中的具体错误消息
5. **本地测试**：确保应用程序在本地环境中成功构建

## 定价和计划

### Libra 免费使用吗？

是的！Libra 提供慷慨的免费层，非常适合学习和小型项目。我们的定价结构包括：

- **免费层**：个人项目和学习的核心功能
- **专业计划**：为专业开发人员提供高级功能
- **MAX 计划**：无限制访问所有功能
- **企业版**：为大型组织提供定制解决方案

### 免费层包含什么？

免费层包括：
- 完全访问 AI 代码生成器
- 基本项目管理
- 通过社区论坛的社区支持
- 自托管能力
- 核心 UI 组件和模板

### 付费计划如何计费？

定价基于使用情况和访问的功能。我们提供透明、可扩展的定价，无隐藏费用。在我们的网站上了解更多[定价详情](https://libra.dev/pricing)。

### 我可以升级或降级我的计划吗？

是的，您可以随时更改计划。升级、降级在当前计费周期结束时生效。

## 社区和支持

### 如何获得帮助？

我们提供多个支持渠道：

| 问题类型 | 推荐渠道 | 响应时间 |
|------------|-------------------|---------------|
| 🐛 **错误报告** | [GitHub Issues](https://github.com/nextify-limited/libra/issues) | 1-2 个工作日 |
| 💡 **功能请求** | [GitHub Discussions](https://github.com/nextify-limited/libra/discussions) | 3-5 个工作日 |
| ❓ **使用问题** | [社区论坛](https://forum.libra.dev) | 实时响应 |
| 🏢 **商务咨询** | [<EMAIL>](mailto:<EMAIL>) | 1 个工作日 |

### 如何为 Libra 做贡献？

我们欢迎各种形式的贡献：

- 🐛 **报告问题**：通过 GitHub Issues
- 💡 **功能建议**：通过 GitHub Discussions
- 🔧 **代码贡献**：提交 Pull Request
- 📖 **文档改进**：增强指南和 API 文档

详细的贡献指南可在我们的 [CONTRIBUTING.md](https://github.com/nextify-limited/libra/blob/main/CONTRIBUTING.md) 中找到。

### 有社区论坛吗？

有的！加入我们活跃的社区论坛：[forum.libra.dev](https://forum.libra.dev)，您可以：

- 从社区和团队获得实时帮助
- 分享您的项目并获得反馈
- 参与每周技术讲座
- 访问独家赞助商频道（适用于赞助商）
- 抢先体验新功能

### 如何了解新功能的最新信息？

关注这些渠道获取最新更新：

- 🐦 **Twitter**：[@nextify2024](https://twitter.com/nextify2024) 获取开发更新
- 💬 **论坛**：[加入我们的社区](https://forum.libra.dev) 获取公告
- 📖 **文档**：[docs.libra.dev](https://docs.libra.dev/) 获取功能指南
- 🌐 **网站**：[libra.dev](https://libra.dev) 获取主要版本发布

## 高级功能

### 我可以用自定义功能扩展 Libra 吗？

当然可以！Libra 设计为可扩展：

- **自定义组件**：创建并集成您自己的 UI 组件
- **API 扩展**：添加新的 tRPC 路由器和端点
- **数据库架构**：使用自定义表和关系扩展数据库
- **中间件**：实现自定义认证和授权逻辑
- **集成**：连接第三方服务和 API

### 如何与外部服务集成？

Libra 支持与各种外部服务集成：

- **支付处理**：Stripe 集成（即将推出）
- **认证**：自定义认证提供商和 SSO
- **存储**：文件上传和管理服务
- **分析**：跟踪和监控工具
- **邮件**：事务性邮件服务

### 我可以为客户白标 Libra 吗？

可以！在 AGPL-3.0 许可证下，您可以：
- ✅ 将 Libra 用于商业项目
- ✅ 修改和自定义平台
- ✅ 为客户部署（需适当归属）
- ✅ 提供基于 Libra 的服务

但是，您必须：
- ⚠️ 如果分发修改版本，保持源代码开放
- ⚠️ 为 Libra 项目提供适当归属
- ⚠️ 将任何改进分享回社区

有关商业许可选项，请联系我们：[<EMAIL>](mailto:<EMAIL>)。

### 如何备份和导出我的项目？

目前，项目数据存储在您配置的数据库中。我们建议：

1. **数据库备份**：定期备份您的 PostgreSQL 和 D1 数据库
2. **代码导出**：从平台下载您生成的代码
3. **环境配置**：保留环境配置的副本
4. **资产备份**：备份任何上传的文件和媒体

## 还有问题？

如果您在此常见问题中找不到问题的答案，我们随时为您提供帮助：

- 💬 **加入我们的论坛**：[forum.libra.dev](https://forum.libra.dev) 获取实时社区支持
- 📧 **发邮件给我们**：[<EMAIL>](mailto:<EMAIL>) 进行商务咨询
- 🐛 **报告问题**：[GitHub Issues](https://github.com/nextify-limited/libra/issues) 报告错误和技术问题
- 💡 **请求功能**：[GitHub Discussions](https://github.com/nextify-limited/libra/discussions) 提出功能建议

我们的社区和团队随时准备帮助您在 Libra AI 上取得成功！
