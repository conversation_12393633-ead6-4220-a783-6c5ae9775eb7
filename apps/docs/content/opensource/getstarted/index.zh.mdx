---
title: "介绍"
mode: "center"
icon: Album
---

import { Accordion, Accordions } from 'fumadocs-ui/components/accordion';
import { Tabs } from 'fumadocs-ui/components/tabs';
import { Platinum } from '../../../components/platinum';
import { Gold } from '../../../components/gold';
import { Silver } from '../../../components/silver';
import { Bronze } from '../../../components/bronze';

<div className="flex flex-col items-stretch text-center">
    <div style={{height:"170px"}}>

<img  className="block dark:hidden mx-auto my-0! h-full" alt="Libra logo" src="/logo.svg"  />
<img  className="hidden dark:block mx-auto my-0! h-full" alt="Libra logo" src="/logo-dark.svg"  />
    </div>
    <h1 className="text-3xl font-bold">Libra</h1>
    <p className="mt-2 mb-1"></p>
    <div  className="flex flex-row flex-wrap justify-center gap-1 py-2">
        <a className="border-none" href="https://github.com/nextify-limited/nextify-limited/actions/workflows/check.yml">
            <img className="h-[20px] m-0!" src="https://img.shields.io/github/actions/workflow/status/nextify-limited/nextify-limited/ci.yml?label=ci" alt="GitHub Actions Workflow Status" />
        </a>
        <a className="border-none" href="https://github.com/nextify-limited/nextify-limited/blob/main/LICENSE">
            <img className="h-[20px] m-0!" src="https://img.shields.io/badge/License-AGPL-green.svg" alt="GitHub License" />
        </a>
        <a className="border-none" href="https://forum.libra.dev">
            <img className="h-[20px] m-0!" src="https://img.shields.io/badge/Community-Forum-7b8dcd" alt="Community Forum" />
        </a>
        <a className="border-none" href="https://x.com/nextify2024">
            <img className="h-[20px] m-0!" src="https://img.shields.io/badge/made_by-nextify2024-blue?color=FF782B&link=https://x.com/nextify2024" alt="Made by Nextify2024" />
        </a>
        <a className="border-none" href="https://github.com/nextify-limited/libra" rel="nofollow">
            <img  className="h-[20px] m-0!" src="https://img.shields.io/github/stars/nextify-limited/nextify-limited" alt="stars" />
        </a>
    </div>
    <div className="flex flex-row justify-center gap-1">
        <a href="https://libra.dev">官网</a>
        <span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
        <a href="https://forum.libra.dev">论坛</a>
        <span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
        <a href="https://twitter.com/nextify2024">𝕏</a>
        <span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
        <br />
    </div>
</div>

## 介绍

Libra AI 是一个智能开发平台，让各种技能水平的用户都能轻松构建全栈 Web 应用程序。
只需用自然语言描述您的想法，Libra 就能立即将您的创意转化为功能完整的 Web 应用和网站。
无需编程经验——开启革命性的开发之旅，让您从概念到部署的速度比以往任何时候都快。

## 功能特性

- **AI 驱动开发**：将自然语言描述转化为功能完整的应用程序
- **零编程要求**：无需编写任何代码即可构建复杂的 Web 应用程序
- **全栈能力**：创建前端界面和后端功能
- **实时预览**：在描述过程中看到应用程序的实时效果
- **现代技术栈**：采用最新的 Web 技术和最佳实践构建
- **即时部署**：一键部署您的应用程序
- **协作环境**：与团队成员在同一工作空间中协作
- **广泛集成**：连接流行的工具和服务

## 您可以构建什么

您可以使用 Libra 构建网站和基于 JavaScript 的全栈 Web 应用程序。如需灵感，请查看我们的展示，其中包含通过 Libra 成功发布的应用程序。

您可以创建各种应用程序，包括：

- **内容网站**：博客、作品集、营销网站和文档
- **电商平台**：具有支付处理和库存管理的在线商店
- **生产力应用**：任务管理器、笔记应用和协作工具
- **社交应用**：社区平台、论坛和社交网络
- **商业工具**：CRM 系统、仪表板和分析平台
- **教育平台**：学习管理系统和课程网站

### 支持的技术

Libra 与现代 Web 开发工具的综合生态系统集成：

**设计与原型**
- Figma 用于设计协作和资源导入（即将推出）

**部署与托管**
- Cloudflare 用于边缘部署和性能优化

**后端服务**
- 即将推出

**开发与协作**
- GitHub 用于版本控制、备份和团队协作（即将推出）

**支付处理**
- Stripe 用于安全支付处理和订阅管理（即将推出）

在我们的[支持技术](/docs/technologies)指南中了解更多关于我们完整的支持技术列表和推荐架构模式。

## 面向技术用户和学习者

Libra 旨在帮助用户构建从简单的内容导向网站（如博客）到复杂的全栈 Web 应用程序（如生产力应用）的任何内容。它需要用户清晰的沟通，并受益于对 Web 开发概念的一些理解。

### 无编程经验入门

开始时您不需要知道如何编程。使用 Libra 构建是学习 Web 开发的绝佳方式，以实用、动手的方式进行，AI 支持始终可用来指导您完成整个过程。

### 成功所需条件

要充分利用 Libra，请明确以下内容：

- **您想要构建什么**：描述您的应用程序的目的和功能
- **用户体验目标**：用户应该如何与您的应用程序交互
- **成功标准**：成功的实现是什么样的
- **设计偏好**：视觉风格、布局和品牌要求

为了帮助您入门，请阅读我们在[最佳实践](/docs/best-practices)中的指南，了解如何有效提示并在不同开发模式下与 AI 协作。

## Libra 和生态系统

Libra 建立在现代 Web 技术的基础上，与更广泛的开发生态系统无缝集成。我们的平台利用：

- **先进的 AI 模型**：用于代码生成的最先进语言模型
- **云基础设施**：可扩展且可靠的托管解决方案
- **现代框架**：React、Next.js 和其他流行框架
- **开发工具**：集成开发环境与协作功能

## 定价

Libra 提供灵活的定价计划，以适应不同的需求和使用模式。我们的定价设计为透明且可扩展：

- **免费层**：非常适合学习和小型项目
- **专业计划**：为专业开发人员提供高级功能
- **MAX 计划**：无限制访问所有功能
- **企业版**：为大型组织提供定制解决方案

定价基于使用情况和访问的功能。在我们的网站上了解更多关于[令牌效率](/docs/tokens)和[定价详情](/pricing)。

<br/>

## 社区和支持

加入我们不断增长的开发者和创作者社区：

- **论坛**：获得帮助、分享项目并与其他用户联系
- **GitHub**：为开源组件做贡献并报告问题
- **文档**：全面的指南和教程
{/* - **视频教程**：逐步演练和用例 */}

## 赞助商

我们感谢赞助商的支持，他们帮助使 Libra AI 成为可能。任何级别的赞助都受到赞赏，并帮助我们继续开发和改进平台。

如果您使用 Libra 构建了成功的产品或想要支持开源 AI 开发，请考虑加入我们的[企业赞助计划](https://github.com/sponsors/nextify-limited)。

### 赞助层级

我们的赞助计划提供多个层级，旨在为赞助商提供价值，同时支持 Libra 生态系统：

#### 白金层级 - $2,000+/月
高级展示位置，专门的展示空间、详细的公司简介，以及在所有 Libra 平台上的最大可见性。

<Platinum locale="zh" />

<br/>

#### 黄金层级 - $500+/月
特色展示位置，包含公司徽标、描述和在我们赞助商展示中的突出位置。

<Gold locale="zh" />

<br/>

#### 白银层级 - $100+/月
在我们的赞助商网格中展示徽标，并直接链接到您的网站或产品。

<Silver locale="zh" />

<br/>

#### 青铜层级 - $25+/月
在我们的社区赞助商部分获得认可，展示徽标和网站链接。

<Bronze locale="zh" />

<br/>

### 所有赞助商的福利

- **社区认可**：在我们的文档和网站中获得认可
- **GitHub 赞助商徽章**：在您的 GitHub 个人资料上获得官方赞助商状态
- **论坛访问权限**：独家访问我们社区论坛中的赞助商频道
- **抢先体验**：预览即将推出的功能并提供反馈
- **支持优先级**：技术支持请求的更快响应时间

准备成为赞助商？访问我们的 [GitHub 赞助商页面](https://github.com/sponsors/nextify-limited)开始。
